import 'package:flutter/foundation.dart';
import 'package:room_eight/core/api_config/client/api_client.dart';
import 'package:room_eight/core/api_config/endpoints/api_endpoint.dart';
import 'package:room_eight/models/auth_model/login_model.dart';
import 'package:room_eight/models/auth_model/profile_detail_model.dart';
import 'package:room_eight/models/auth_model/signup_model.dart';

class AuthRepository {
  final ApiClient apiClient;
  AuthRepository({required this.apiClient});

  Future<SignUpResponse> signUpCall({
    required String name,
    required String signUpEmail,
    required String signUpPassword,
  }) async {
    try {
      final Map<String, dynamic> data = {
        'name': name,
        'email': signUpEmail,
        'password': signUpPassword,
      };
      var response = await apiClient.request(
        RequestType.POST,
        ApiEndPoint.signUpUrl,
        data: data,
      );
      return await compute(SignUpResponse.fromJson, response);
    } catch (error) {
      rethrow;
    }
  }

  Future<LoginResponse> loginCall({
    required String loginEmail,
    required String loginPassword,
  }) async {
    try {
      final Map<String, dynamic> data = {
        'email': loginEmail,
        'password': loginPassword,
      };
      var response = await apiClient.request(
        RequestType.POST,
        ApiEndPoint.loginUpUrl,

        data: data,
      );
      return await compute(LoginResponse.fromJson, response);
    } catch (error) {
      rethrow;
    }
  }

  Future<UserProfile> profileDetailCall({
    required Map<String, dynamic> data,
    required String profilePicture,
    required List<String> profilePicturesList,
  }) async {
    try {
      var response = await apiClient.multipartRequestWithMixedData(
        path: ApiEndPoint.createUserProfileUrl,
        singleImageKey: 'profile_picture',
        singleImagePath: profilePicture,
        multiImageKey: 'profile_pictures',
        multiImagePaths: profilePicturesList,
        data: data,
      );
      return await compute(UserProfile.fromJson, response);
    } catch (error) {
      rethrow;
    }
  }
}
