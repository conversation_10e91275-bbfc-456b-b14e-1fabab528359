import 'dart:io';
import 'package:path_provider/path_provider.dart';

/// <PERSON>ript to manually clear Flutter cache manager cache files
/// Run with: dart run scripts/clear_cache.dart
void main() async {
  try {
    print('Starting cache cleanup...');

    // Get the application documents directory
    final Directory appDocDir = await getApplicationDocumentsDirectory();
    final String cachePath = '${appDocDir.path}/cache';

    print('Cache directory: $cachePath');

    // Check if cache directory exists
    final Directory cacheDir = Directory(cachePath);
    if (await cacheDir.exists()) {
      print('Cache directory found. Clearing...');

      // Delete all cache files
      await cacheDir.delete(recursive: true);
      print('Cache directory deleted successfully.');

      // Recreate the cache directory
      await cacheDir.create(recursive: true);
      print('Cache directory recreated.');
    } else {
      print('Cache directory does not exist. Nothing to clear.');
    }

    // Also try to clear specific cache manager files
    final List<String> cacheFiles = [
      '${appDocDir.path}/RoomEight.db',
      '${appDocDir.path}/RoomEight_V2.db',
      '${appDocDir.path}/RoomEight_V3.db',
    ];

    for (String filePath in cacheFiles) {
      final File file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        print('Deleted cache file: $filePath');
      }
    }

    print('Cache cleanup completed successfully!');
  } catch (e) {
    print('Error during cache cleanup: $e');
    exit(1);
  }
}
