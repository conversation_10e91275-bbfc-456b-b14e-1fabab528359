import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:room_eight/core/api_config/client/api_client.dart';
import 'package:room_eight/core/check_connection/check_connection_cubit.dart';
import 'package:room_eight/core/l10n/bloc/locale_bloc.dart';
import 'package:room_eight/core/l10n/bloc/locale_event.dart';
import 'package:room_eight/core/themes/bloc/theme_bloc.dart';
import 'package:room_eight/repository/auth_repository.dart';
import 'package:room_eight/repository/profile_repository.dart';
import 'package:room_eight/viewmodels/auth_bloc/auth_bloc.dart';
import 'package:room_eight/viewmodels/like_bloc/like_bloc.dart';
import 'package:room_eight/viewmodels/nav_bloc/nav_bloc.dart';
import 'package:room_eight/viewmodels/onboarding_bloc/onboarding_bloc.dart';
import 'package:room_eight/viewmodels/profile_bloc/profile_bloc.dart';
import 'package:room_eight/viewmodels/splash_bloc/splash_bloc.dart';
import 'package:room_eight/viewmodels/home_bloc/home_bloc.dart';

class BlocProviders extends StatelessWidget {
  final Widget child;

  const BlocProviders({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<LocaleBloc>(
          create: (context) =>
              LocaleBloc()..add(SetLocale(locale: Locale('en'))),
        ),
        BlocProvider<ThemeBloc>(
          create: (context) => ThemeBloc()
            ..add(
              InitializeTheme(isDarkThemeOn: false, followSystemTheme: true),
            ),
        ),
        BlocProvider(
          create: (_) => CheckConnectionCubit()..initializeConnectivity(),
        ),

        BlocProvider<SplashBloc>(
          create: (context) =>
              SplashBloc(AuthRepository(apiClient: ApiClient()))
                ..add(SalesAppAuthIntilizeEvent()),
        ),
        BlocProvider<OnboardingBloc>(create: (context) => OnboardingBloc()),
        BlocProvider<AuthBloc>(
          create: (context) => AuthBloc(AuthRepository(apiClient: ApiClient())),
        ),

        BlocProvider<NavBloc>(create: (context) => NavBloc()),
        BlocProvider<HomeBloc>(create: (context) => HomeBloc()),
        BlocProvider<ProfileBloc>(create: (context) => ProfileBloc(ProfileRepository(apiClient: ApiClient()))),
        BlocProvider<LikeBloc>(create: (context) => LikeBloc()),
      ],
      child: child,
    );
  }
}
