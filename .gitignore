# See https://www.dartlang.org/guides/libraries/private-files

# Files and directories created by pub
.dart_tool/
.packages
build/
# If you're building an application, you may want to check-in your pubspec.lock
pubspec.lock

# Directory created by dartdoc
# If you don't generate documentation locally you can remove this line.
doc/api/

# dotenv environment variables file
.env*

# Avoid committing generated Javascript files:
*.dart.js
# Produced by the --dump-info flag.
*.info.json
# When generated by dart2js. Don't specify *.js if your
# project includes source files written in JavaScript.
*.js
*.js_
*.js.deps
*.js.map

.flutter-plugins
.flutter-plugins-dependencies
lib/core/generated/intl/messages_all.dart
lib/core/generated/intl/messages_en.dart
lib/core/generated/l10n.dart
lib/core/generated/fonts.gen.dart
lib/core/generated/assets.gen.dart
